defmodule Drops.Relations.CompositionTest do
  use Drops.RelationCase, async: false

  describe "restrict/1" do
    @tag relations: [:users]
    test "composing", %{users: users} do
      users.insert(%{name: "<PERSON>", email: "<EMAIL>"})
      users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

      relation =
        users
        |> users.restrict(name: "<PERSON>")
        |> users.restrict(email: "<EMAIL>")

      # Test both Enum.at and List.first (which should work via Enumerable)
      assert jane = Enum.at(relation, 0)

      # Also test that we can convert to list and use List.first
      relation_list = Enum.to_list(relation)
      assert jane_from_list = List.first(relation_list)
      assert jane_from_list.name == jane.name

      assert jane.name == "<PERSON>"
      assert jane.email == "<EMAIL>"
    end
  end

  describe "get_by_email/1 - auto-generated composable finders" do
    @tag relations: [:users]
    test "composing", %{users: users} do
      users.insert(%{name: "<PERSON>", email: "<EMAIL>"})
      users.insert(%{name: "<PERSON>", email: "<EMAIL>"})

      relation =
        users.get_by_email("<EMAIL>")
        |> users.restrict(name: "<PERSON>")

      # Test both Enum.at and List.first (which should work via Enumerable)
      assert jane = Enum.at(relation, 0)

      # Also test that we can convert to list and use List.first
      relation_list = Enum.to_list(relation)
      assert jane_from_list = List.first(relation_list)
      assert jane_from_list.name == jane.name

      assert jane.name == "Jane"
      assert jane.email == "<EMAIL>"
    end
  end

  @tag relations: [:users]
  test "enumerable protocol works with various functions", %{users: users} do
    users.insert(%{name: "Alice", email: "<EMAIL>"})
    users.insert(%{name: "Bob", email: "<EMAIL>"})
    users.insert(%{name: "Charlie", email: "<EMAIL>"})

    relation = users |> users.restrict(name: "Alice")

    # Test Enum.count
    assert Enum.count(relation) == 1

    # Test Enum.map
    emails = Enum.map(relation, & &1.email)
    assert emails == ["<EMAIL>"]

    # Test Enum.filter (should work on the materialized list)
    filtered = Enum.filter(relation, fn user -> String.contains?(user.email, "alice") end)
    assert length(filtered) == 1

    # Test Enum.any?
    assert Enum.any?(relation, fn user -> user.name == "Alice" end)
    refute Enum.any?(relation, fn user -> user.name == "Bob" end)
  end

  @tag relations: [:users]
  test "enumerable protocol works with empty results", %{users: users} do
    users.insert(%{name: "Alice", email: "<EMAIL>"})

    # Create a relation that should return no results
    relation = users |> users.restrict(name: "NonExistent")

    # Test with empty results
    assert Enum.count(relation) == 0
    assert Enum.to_list(relation) == []
    assert Enum.at(relation, 0) == nil
    refute Enum.any?(relation, fn _ -> true end)
  end

  @tag relations: [:users]
  test "ecto queryable protocol works", %{users: users} do
    users.insert(%{name: "Alice", email: "<EMAIL>"})
    users.insert(%{name: "Bob", email: "<EMAIL>"})

    # Test that relation structs can be used in Ecto queries
    relation = users |> users.restrict(name: "Alice")

    # Convert to Ecto.Query and verify it works
    query = Ecto.Queryable.to_query(relation)
    assert %Ecto.Query{} = query

    # Test that the relation module itself can be used as queryable
    module_query = Ecto.Queryable.to_query(users)
    assert %Ecto.Query{} = module_query
  end
end
