defmodule Drops.Relation.AssociationsTest do
  use Drops.RelationCase, async: false

  describe "defining associations" do
    relation(:user_groups) do
      schema("user_groups") do
        belongs_to(:user, Test.Relations.Users)
        belongs_to(:group, Test.Relations.Groups)
      end
    end

    relation(:users) do
      schema("users") do
        has_many(:user_groups, Test.Relations.UserGroups)
        has_many(:groups, through: [:user_groups, :group])
      end
    end

    relation(:groups) do
      schema("groups") do
        has_many(:user_groups, Test.Relations.UserGroups)
        has_many(:users, through: [:user_groups, :user])
      end
    end

    test "returns relation view", %{users: users, groups: groups, user_groups: user_groups} do
      {:ok, user} = users.insert(%{name: "<PERSON>"})
      {:ok, group} = groups.insert(%{name: "Ad<PERSON>"})

      user_groups.insert(%{user_id: user.id, group_id: group.id})

      user = users.preload(:groups) |> Enum.at(0)

      assert %{name: "<PERSON>", groups: [%{name: "<PERSON><PERSON>"}]} = user
    end
  end
end
