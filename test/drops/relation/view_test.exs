defmodule Drops.Relation.ViewTest do
  use Drops.RelationCase, async: false

  describe "defining a relation view" do
    relation(:users) do
      view(:active) do
        schema([:id, :name, :active])

        relation do
          restrict(active: true)
        end
      end
    end

    test "returns relation view", %{users: users} do
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})
      users.insert(%{name: "<PERSON>", active: false})
      users.insert(%{name: "<PERSON>", active: true})

      assert [%{name: "<PERSON>"}, %{name: "<PERSON>"}] = users.active() |> Enum.to_list()
    end
  end
end
