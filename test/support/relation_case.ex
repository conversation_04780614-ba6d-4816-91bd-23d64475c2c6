defmodule Drops.RelationCase do
  use ExUnit.CaseTemplate

  using do
    quote do
      use Drops.Relation.DoctestCase

      alias Drops.Relation.Repos.Sqlite
      alias Drops.Relation.Repos.Postgres

      import Drops.RelationCase
    end
  end

  setup tags do
    # Determine adapter from tags, environment variable, or default to :sqlite
    adapter =
      tags[:adapter] ||
        (System.get_env("ADAPTER") &&
           String.downcase(System.get_env("ADAPTER")) |> String.to_atom()) ||
        :sqlite

    setup_sandbox(tags, adapter)

    context = handle_relation_tags(tags, adapter)

    # Set up cleanup for relation modules
    on_exit(fn -> cleanup_modules(Map.values(context)) end)

    # Add adapter and repo to context
    context =
      Map.merge(context, %{
        adapter: adapter,
        repo: get_repo_for_adapter(adapter)
      })

    {:ok, context}
  end

  def cleanup_modules(relation_modules) when is_list(relation_modules) do
    Enum.each(relation_modules, &cleanup_modules/1)
  end

  def cleanup_modules(relation_module) do
    if Code.ensure_loaded?(relation_module) do
      Enum.each([relation_module, relation_module.__schema_module__()], fn module ->
        for protocol <- [Enumerable, Ecto.Queryable] do
          impl_module = Module.concat([protocol, module])

          if Code.ensure_loaded?(impl_module) do
            :code.delete(impl_module)
            :code.purge(impl_module)
          end
        end

        :code.delete(module)
        :code.purge(module)
      end)
    end
  end

  @doc """
  Defines a relation for testing.

  ## Examples

      relation(:users)
      relation(:posts) do
        # Custom relation configuration
      end
  """
  defmacro relation(name, opts) do
    quote do
      setup context do
        adapter = Map.get(context, :adapter, :sqlite)
        table_name = Atom.to_string(unquote(name))

        relation_module =
          Module.concat([
            Test,
            Relations,
            Macro.camelize(Atom.to_string(adapter)),
            Macro.camelize(table_name)
          ])

        cleanup_modules(relation_module)

        repo_module =
          case adapter do
            :sqlite -> Drops.Relation.Repos.Sqlite
            :postgres -> Drops.Relation.Repos.Postgres
          end

        {:ok, _} = Drops.Relation.Cache.warm_up(repo_module, [table_name])

        block = Keyword.get(unquote(Macro.escape(opts)), :do, [])

        {:module, ^relation_module, _bytecode, _result} =
          Module.create(
            relation_module,
            quote do
              use Drops.Relation, repo: unquote(repo_module), name: unquote(table_name)

              alias Test.Relations

              unquote(block)
            end,
            Macro.Env.location(__ENV__)
          )

        # Add relation to context
        relation_context = Map.put(context, unquote(name), relation_module)

        on_exit(fn -> cleanup_modules(relation_module) end)

        {:ok, relation_context}
      end
    end
  end

  @doc """
  Runs tests for multiple adapters.

  ## Examples

      adapters([:sqlite, :postgres]) do
        @tag relations: [:users]
        test "works with both adapters", %{users: users} do
          # This test will run for both Sqlite and PostgreSQL
        end
      end
  """
  defmacro adapters(adapter_list, do: block) do
    for adapter <- adapter_list do
      quote do
        describe "with #{unquote(adapter)} adapter" do
          setup do
            {:ok, adapter: unquote(adapter)}
          end

          unquote(block)
        end
      end
    end
  end

  @doc """
  Sets up the sandbox based on the test tags and adapter.
  """
  def setup_sandbox(tags, adapter) do
    Drops.Relation.Repos.start_owner!(adapter, shared: not tags[:async])
    on_exit(fn -> Drops.Relation.Repos.stop_owner(adapter) end)
  end

  @doc """
  Handles @tag relations: [...] and @describetag relations: [...] syntax.
  Returns {context, cleanup_modules} where cleanup_modules is a list of modules to clean up.
  """
  def handle_relation_tags(tags, adapter) do
    relations = tags[:relations] || []
    repo = get_repo_for_adapter(adapter)

    table_names = Enum.map(relations, &Atom.to_string/1)

    # Always force refresh in tests to ensure fresh schema inference
    case Drops.Relation.Cache.refresh(repo, table_names) do
      :ok -> :ok
      {:error, reason} -> raise "Failed to refresh cache: #{inspect(reason)}"
    end

    Enum.reduce(relations, %{}, fn relation_name, context ->
      relation_name_string = Atom.to_string(relation_name)
      relation_module_name = relation_name_string |> Macro.camelize()

      module_name =
        Module.concat([
          Test,
          Relations,
          Macro.camelize(Atom.to_string(tags[:adapter])),
          relation_module_name
        ])

      cleanup_modules(module_name)

      {:module, ^module_name, _bytecode, _result} =
        Module.create(
          module_name,
          quote do
            use Drops.Relation, repo: unquote(repo), name: unquote(relation_name_string)
          end,
          Macro.Env.location(__ENV__)
        )

      Map.put(context, relation_name, module_name)
    end)
  end

  @doc """
  Gets the appropriate repo module for the given adapter.
  """
  def get_repo_for_adapter(:sqlite), do: Drops.Relation.Repos.Sqlite
  def get_repo_for_adapter(:postgres), do: Drops.Relation.Repos.Postgres

  @doc """
  Helper for asserting column properties in SQL Database tables.

  ## Examples

      assert_column(table, :id, :integer, primary_key: true)
      assert_column(table, :email, :string, nullable: true, default: nil)
  """
  def assert_column(table, column_name, expected_type, opts \\ []) do
    column = table[column_name]

    assert column != nil, "Column #{column_name} not found in table"

    assert column.type == expected_type,
           "Expected column #{column_name} to have type #{inspect(expected_type)}, got #{inspect(column.type)}"

    Enum.each(opts, fn {key, expected_value} ->
      actual_value = Map.get(column.meta, key)

      assert actual_value == expected_value,
             "Expected column #{column_name} to have #{key}: #{inspect(expected_value)}, got #{inspect(actual_value)}"
    end)
  end

  @doc """
  Helper for asserting field properties in Relation schemas.

  ## Examples

      assert_field(schema, :id, :id, primary_key: true, type: :integer)
      assert_field(schema, :email, :string, nullable: true)
  """
  def assert_field(schema, field_name, expected_type, opts \\ []) do
    field = Drops.Relation.Schema.find_field(schema, field_name)

    assert field != nil, "Field #{field_name} not found in schema"

    assert field.type == expected_type,
           "Expected field #{field_name} to have type #{inspect(expected_type)}, got #{inspect(field.type)}"

    Enum.each(opts, fn {key, expected_value} ->
      actual_value = Map.get(field.meta, key, Map.get(field, key))

      assert actual_value == expected_value,
             "Expected field #{field_name} to have #{key}: #{inspect(expected_value)}, got #{inspect(actual_value)}"
    end)
  end
end
