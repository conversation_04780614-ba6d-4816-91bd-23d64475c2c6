defmodule Users do
  use Drops.Relation, repo: Drops.Relation.Repos.Postgres, name: "users"

  view(:active) do
    schema([:id, :name, :active])

    relation do
      restrict(active: true)
    end
  end
end

Users.insert(%{name: "<PERSON>", active: false})
Users.insert(%{name: "<PERSON>", active: true})
Users.insert(%{name: "<PERSON>", active: false})
Users.insert(%{name: "<PERSON>", active: true})
